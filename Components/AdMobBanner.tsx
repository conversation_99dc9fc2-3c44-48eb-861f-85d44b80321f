import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { BannerAd, BannerAdSize } from 'react-native-google-mobile-ads';
import { Colors, Spacing } from '../theme';

interface AdMobBannerProps {
  size?: BannerAdSize;
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
  adUnitId?: string; // Allow custom ad unit ID
}

const AdMobBanner: React.FC<AdMobBannerProps> = ({
  size = BannerAdSize.BANNER,
  style,
  onAdLoaded,
  onAdFailedToLoad,
  adUnitId: customAdUnitId,
}) => {
  const [, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Use the correct ad unit ID that's not configured for partner bidding
  // Ad unit "Ad one" (ca-app-pub-8809398979690427/3899304178) is the only one available for direct AdMob use
  // The other ad units have partner bidding enabled and cannot be used with AdMob directly
  const productionAdUnitId = Platform.select({
    ios: 'ca-app-pub-8809398979690427/3899304178', // "Ad one" - available for AdMob
    android: 'ca-app-pub-8809398979690427/3899304178', // Using same ID for Android
  }) || 'ca-app-pub-8809398979690427/3899304178';

  // FORCE PRODUCTION AD UNIT - no fallback to see actual errors
  const adUnitId = customAdUnitId || productionAdUnitId;

  // Debug logging
  useEffect(() => {
    console.log('🎯 AdMob Banner: Component mounted');
    console.log('🎯 AdMob Banner: Using ad unit ID:', adUnitId);
    console.log('🎯 AdMob Banner: Platform:', Platform.OS);
    console.log('🎯 AdMob Banner: Mode: PRODUCTION (no fallback)');
  }, [adUnitId]);

  const handleAdLoaded = () => {
    console.log('✅ AdMob Banner: Ad loaded successfully');
    setIsLoaded(true);
    setHasError(false);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    console.log('❌ AdMob Banner: Failed to load ad:', error);
    console.log('❌ AdMob Banner: Error details:', JSON.stringify(error, null, 2));
    console.log('❌ AdMob Banner: Ad Unit ID that failed:', adUnitId);
    console.log('❌ AdMob Banner: Platform:', Platform.OS);

    // TEMPORARILY DISABLED: Fallback system to see actual production ad errors
    // if (!useTestAd && !customAdUnitId) {
    //   console.log('🔄 AdMob Banner: Trying test ad as fallback...');
    //   setUseTestAd(true);
    //   setHasError(false);
    //   return;
    // }

    setIsLoaded(false);
    setHasError(true);
    onAdFailedToLoad?.(error);
  };

  // Show debug info if there's an error
  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Text style={styles.debugText}>AdMob Banner: Failed to load</Text>
        <Text style={styles.debugText}>Ad Unit: {adUnitId}</Text>
        <Text style={styles.debugText}>Mode: Production Ad (no fallback)</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['health', 'food', 'cosmetics', 'wellness'],
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: Spacing.Small,
  },
  errorContainer: {
    backgroundColor: Colors.Error,
    padding: Spacing.Small,
  },
  debugText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default AdMobBanner;
