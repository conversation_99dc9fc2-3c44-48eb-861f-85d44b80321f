import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import { Colors, Spacing } from '../theme';

interface AdMobBannerProps {
  size?: BannerAdSize;
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
  adUnitId?: string; // Allow custom ad unit ID
}

const AdMobBanner: React.FC<AdMobBannerProps> = ({
  size = BannerAdSize.BANNER,
  style,
  onAdLoaded,
  onAdFailedToLoad,
  adUnitId: customAdUnitId,
}) => {
  const [, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Use test ad unit IDs in development, production IDs in production
  const adUnitId = customAdUnitId || (__DEV__ ? TestIds.BANNER : Platform.select({
    ios: 'ca-app-pub-8809398979690427/3899304178', // Your iOS banner ad unit (correct ID)
    android: 'ca-app-pub-8809398979690427/3899304178', // Your Android banner ad unit (using iOS ID for now)
  })) || TestIds.BANNER;

  // Debug logging
  useEffect(() => {
    console.log('🎯 AdMob Banner: Component mounted');
    console.log('🎯 AdMob Banner: Using ad unit ID:', adUnitId);
    console.log('🎯 AdMob Banner: Platform:', Platform.OS);
  }, [adUnitId]);

  const handleAdLoaded = () => {
    console.log('✅ AdMob Banner: Ad loaded successfully');
    setIsLoaded(true);
    setHasError(false);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    console.log('❌ AdMob Banner: Failed to load ad:', error);
    setIsLoaded(false);
    setHasError(true);
    onAdFailedToLoad?.(error);
  };

  // Show debug info if there's an error
  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Text style={styles.debugText}>AdMob Banner: Failed to load</Text>
        <Text style={styles.debugText}>Ad Unit: {adUnitId}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['health', 'food', 'cosmetics', 'wellness'],
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: Spacing.Small,
  },
  errorContainer: {
    backgroundColor: Colors.Error,
    padding: Spacing.Small,
  },
  debugText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default AdMobBanner;
