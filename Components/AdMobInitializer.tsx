import React, { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import mobileAds, { MaxAdContentRating } from 'react-native-google-mobile-ads';

interface AdMobInitializerProps {
  children: React.ReactNode;
}

const AdMobInitializer: React.FC<AdMobInitializerProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeAdMob = async () => {
      try {
        console.log('🚀 AdMob: Starting initialization...');
        
        // Initialize the Google Mobile Ads SDK
        await mobileAds().initialize();
        
        // Configure ad settings
        await mobileAds().setRequestConfiguration({
          // Set maximum ad content rating
          maxAdContentRating: MaxAdContentRating.PG,
          
          // Indicate that you want your content treated as child-directed
          tagForChildDirectedTreatment: false,
          
          // Indicate whether you want to be treated as under the age of consent
          tagForUnderAgeOfConsent: false,
          
          // No test device IDs - using real ads in production
          testDeviceIdentifiers: [],
        });

        console.log('✅ AdMob: Initialization completed successfully');
        setIsInitialized(true);
      } catch (error) {
        console.error('❌ AdMob: Initialization failed:', error);
        // Still set as initialized to allow the app to continue
        setIsInitialized(true);
      }
    };

    initializeAdMob();
  }, []);

  // Return children immediately - ads will load when ready
  return <>{children}</>;
};

export default AdMobInitializer;
