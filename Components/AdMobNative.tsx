import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { Colors, Spacing, Shadow } from '../theme';

// Import only the components that actually exist in react-native-google-mobile-ads v15
import {
  NativeAd,
  NativeAdView,
  NativeMediaView
} from 'react-native-google-mobile-ads';

interface AdMobNativeProps {
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

const AdMobNative: React.FC<AdMobNativeProps> = ({
  style,
  onAdLoaded,
  onAdFailedToLoad,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Use production ad unit IDs for real advertisements
  const adUnitId = Platform.select({
    ios: 'ca-app-pub-8809398979690427/4326679585', // Your iOS native ad unit
    android: 'ca-app-pub-8809398979690427/4326679585', // Your Android native ad unit (same for now)
  }) || 'ca-app-pub-8809398979690427/4326679585';

  // Debug logging
  useEffect(() => {
    console.log('🎯 AdMob Native: Component mounted');
    console.log('🎯 AdMob Native: Using ad unit ID:', adUnitId);
    console.log('🎯 AdMob Native: Platform:', Platform.OS);

    // Debug AdMob imports
    console.log('🔍 AdMob Native: Checking imports...');
    console.log('🔍 NativeAd:', typeof NativeAd, NativeAd);
    console.log('🔍 NativeAdView:', typeof NativeAdView, NativeAdView);
    console.log('🔍 NativeMediaView:', typeof NativeMediaView, NativeMediaView);
  }, [adUnitId]);

  const handleAdLoaded = () => {
    console.log('✅ AdMob Native: Ad loaded successfully');
    setIsLoaded(true);
    setHasError(false);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    console.log('❌ AdMob Native: Failed to load ad:', error);
    setIsLoaded(false);
    setHasError(true);
    onAdFailedToLoad?.(error);
  };

  // Show debug info if there's an error
  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Text style={styles.debugText}>AdMob Native: Failed to load</Text>
        <Text style={styles.debugText}>Ad Unit: {adUnitId}</Text>
      </View>
    );
  }

  // Real native ad implementation
  return (
    <View style={[styles.container, style]}>
      <NativeAd
        unitId={adUnitId}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['health', 'food', 'cosmetics', 'wellness'],
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      >
        <NativeAdView style={styles.nativeAdView}>
          <View style={styles.adContent}>
            <Text style={styles.adText}>Sponsored</Text>
            <NativeMediaView style={styles.adMedia} />
          </View>
        </NativeAdView>
      </NativeAd>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 16,
    overflow: 'hidden',
    ...Shadow.Medium,
  },
  errorContainer: {
    backgroundColor: Colors.Error,
    padding: Spacing.Medium,
    minHeight: 120,
  },
  debugText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    textAlign: 'center',
    marginBottom: Spacing.Small,
  },
  nativeAdView: {
    width: '100%',
    padding: Spacing.Medium,
  },
  adContent: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  adText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkText,
    marginBottom: Spacing.Small,
    textAlign: 'center',
  },
  adSubText: {
    fontSize: 12,
    color: Colors.LightText,
    marginBottom: Spacing.Medium,
    textAlign: 'center',
  },
  adMedia: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    backgroundColor: Colors.SurfaceSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 12,
    color: Colors.LightText,
    textAlign: 'center',
  },
});

export default AdMobNative;
