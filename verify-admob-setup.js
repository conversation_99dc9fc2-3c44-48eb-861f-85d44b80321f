#!/usr/bin/env node

// AdMob Setup Verification Script for JunkChk
// This script checks if all AdMob configuration is correct

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying AdMob Setup for JunkChk...\n');

let hasErrors = false;

// Check app.json
try {
  const appJsonPath = path.join(__dirname, 'app.json');
  const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  
  console.log('📱 Checking app.json configuration...');
  
  // Check iOS bundle identifier
  if (appJson.expo.ios?.bundleIdentifier) {
    console.log(`✅ iOS Bundle Identifier: ${appJson.expo.ios.bundleIdentifier}`);
  } else {
    console.log('❌ iOS Bundle Identifier missing');
    hasErrors = true;
  }
  
  // Check iOS AdMob App ID
  if (appJson.expo.ios?.googleMobileAdsAppId) {
    console.log(`✅ iOS AdMob App ID: ${appJson.expo.ios.googleMobileAdsAppId}`);
  } else {
    console.log('❌ iOS AdMob App ID missing');
    hasErrors = true;
  }
  
  // Check plugins
  const plugins = appJson.expo.plugins || [];
  const hasDevClient = plugins.includes('expo-dev-client');
  const hasAdMobPlugin = plugins.some(plugin => 
    Array.isArray(plugin) && plugin[0] === 'react-native-google-mobile-ads'
  );
  
  if (hasDevClient) {
    console.log('✅ expo-dev-client plugin configured');
  } else {
    console.log('❌ expo-dev-client plugin missing');
    hasErrors = true;
  }
  
  if (hasAdMobPlugin) {
    console.log('✅ react-native-google-mobile-ads plugin configured');
  } else {
    console.log('❌ react-native-google-mobile-ads plugin missing');
    hasErrors = true;
  }
  
} catch (error) {
  console.log('❌ Error reading app.json:', error.message);
  hasErrors = true;
}

// Check package.json dependencies
try {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  console.log('\n📦 Checking package.json dependencies...');
  
  const requiredDeps = [
    'react-native-google-mobile-ads',
    'expo-dev-client',
    'expo-build-properties'
  ];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} missing`);
      hasErrors = true;
    }
  });
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  hasErrors = true;
}

// Check AdMob components
console.log('\n🧩 Checking AdMob components...');

const adMobBannerPath = path.join(__dirname, 'Components', 'AdMobBanner.tsx');
if (fs.existsSync(adMobBannerPath)) {
  console.log('✅ AdMobBanner component exists');
  
  // Check if it contains the real ad unit ID
  const bannerContent = fs.readFileSync(adMobBannerPath, 'utf8');
  if (bannerContent.includes('ca-app-pub-8809398979690427/3899304178')) {
    console.log('✅ Real iOS ad unit ID configured');
  } else {
    console.log('⚠️  Still using test ad unit ID (this is OK for testing)');
  }
} else {
  console.log('❌ AdMobBanner component missing');
  hasErrors = true;
}

const adMobServicePath = path.join(__dirname, 'services', 'AdMobService.ts');
if (fs.existsSync(adMobServicePath)) {
  console.log('✅ AdMobService exists');
} else {
  console.log('❌ AdMobService missing');
  hasErrors = true;
}

// Summary
console.log('\n📋 Summary:');
if (hasErrors) {
  console.log('❌ Some issues found. Please check the errors above.');
  console.log('\n🔧 To fix issues, refer to ADMOB_SETUP.md');
} else {
  console.log('✅ All AdMob configuration looks good!');
  console.log('\n🚀 Ready to build:');
  console.log('   ./build-ios.sh');
  console.log('\n📖 For detailed instructions, see ADMOB_SETUP.md');
}

process.exit(hasErrors ? 1 : 0);
